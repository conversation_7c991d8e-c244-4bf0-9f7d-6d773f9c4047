Vamos criar uma landingpage de alta conversao para esse sistema:

Chronos Plataform

Função	Nome sugerido	Hex	Exemplo
Primária	Chronos Maroon	#7B1818	<span style="background:#7B1818;color:#fff;padding:2px 6px;border-radius:2px"> </span>
Secundária	Ouro Antigo	#D4AF37	<span style="background:#D4AF37;color:#000;padding:2px 6px;border-radius:2px"> </span>
Destaque	Violeta Profundo	#6A489F	<span style="background:#6A489F;color:#fff;padding:2px 6px;border-radius:2px"> </span>
Neutra clara	Mármore Ívory	#F5F3EE	<span style="background:#F5F3EE;color:#000;padding:2px 6px;border-radius:2px"> </span>
Neutra escura	Carvão Noturno	#2F2F2F	<span style="background:#2F2F2F;color:#fff;padding:2px 6px;border-radius:2px"> </span>
Apoio quente	Bronze Suave	#8B6F47	<span style="background:#8B6F47;color:#fff;padding:2px 6px;border-radius:2px"> </span>

Sugestões de uso
Chronos Maroon (#7B1818): botões primários, cabeçalhos e elementos de call-to-action.

Ouro Antigo (#D4AF37): ícones, highlights e indicadores de status (ex.: ganhos, badges).

Violeta Profundo (#6A489F): links, hover states e pequenos detalhes para contraste elegante.

Mármore Ívory (#F5F3EE): fundo principal de páginas e cards.

Carvão Noturno (#2F2F2F): texto padrão, barras de navegação e rodapés.

Bronze Suave (#8B6F47): bordas, divisórias e estados secundários de botões.

Segue uma proposta de MVP para o **Projeto Chronos**, dividida em três blocos:

---

As três telas apresentam um design coeso, que combina um estilo “dark mode” sofisticado com toques de dourado e elementos clássicos (estátuas gregas), criando uma atmosfera de exclusividade e urgência. A seguir, a descrição detalhada de cada uma:

---

## 1. Tela Central (Dashboard Principal)

* **Header (Barra superior)**

  * Fundo branco puro.
  * À esquerda, o logotipo “CHRONOS” em fonte sans-serif preta, peso semibold.
  * À direita, ícones discretos (notificações, perfil) e, em destaque, um botão “Depositar” azul-celeste (#3B82F6) com cantos levemente arredondados (≈4 px).
* **Seção Hero**

  * Fundo: bloco escuro (tom de azul-marinho quase preto) com leve textura ou sobreposição de padrão sutil.
  * Título central “CHRONOS” em dourado metálico (#D4AF37), fonte grande (≈48 px), espaçamento entre caracteres aumentado.
  * Abaixo, quatro “cards” hexagonais com bordas suaves, cada um exibindo métricas (e.g. Jackpot 11 000, Diária \$500…) em branco e dourado. Os cartões flutuam sobre o fundo, com sombra suave.
  * Botão CTA “Ver Prêmios” em fundo dourado, texto escuro, cantos arredondados, alinhado ao centro.
* **Seção Sweepstakes**

  * Fundo degradê areia→bege claro, com duas estátuas gregas em sépia (à esquerda e à direita) sobre um pedestal, conferindo ar clássico.
  * No topo do degradê, um card central escuro retangular arredondado exibindo “Participantes 15 000”, “Votos \$900” e “Duração 00:00” em tipografia grande branca e detalhes dourados.
  * Abaixo, um botão “Entrar no Sweepstakes” em dourado, centralizado.

---

## 2. Tela Esquerda (Enter Sweepstakes / Listagem)

* **Layout em duas colunas**

  * **Sidebar (coluna esquerda)**

    * Fundo azul-escuro (próximo ao do hero), largura fixa (\~240 px).
    * Logo reduzida “CHRONOS” no topo e botões de navegação iconográficos em dourado (hover em tom mais claro).
  * **Conteúdo Principal**

    * Fundo escuro uniforme.
    * Título “Enter Sweepstakes” em branco, fonte semibold (\~32 px).
    * Botão “View Sweepstakes” em azul-celeste, cantos arredondados.
    * Abaixo, cards em fundo cinza-escuro mostrando valores de jackpots (\$900, \$700…) com texto grande em branco e subtítulos em cinza-claro.
    * Uso de ícones lineares ao lado de cada item de menu (“Jackpots”, “History”, etc.) em dourado.

---

## 3. Tela Direita (Perfil / Histórico de Depósitos)

* **Header escuro**

  * Mesmo padrão do header branco, mas invertido: fundo azul-marinho, logo e ícones em branco.
  * Botão “Sacar Incentivo” em borda dourada (outline) com texto dourado.
* **Banner de usuário**

  * Foto ou ilustração de guerreiro espartano em tonalidades douradas sobre fundo azul-marinho.
  * Nome “Chronos” em branco, abaixo subtítulo “User balance” e um botão “Depositar” azul-claro.
* **Cards de métricas**

  * Três cartões retangulares, bordas arredondadas (\~8 px), fundo cinza-escuro, exibindo “Total Deposited”, “Total Withdrawn” e “Net Gain” em branco e números em destaque dourado.
* **Lista de transações**

  * Tabela limpa com colunas “Data”, “Tipo”, “Valor”, usando linhas zebra (alternância entre tom muito escuro e ligeiramente menos escuro) para facilitar leitura.
  * Ícones discretos ao lado de cada tipo de transação (seta para cima/baixo) em dourado.

---

### Elementos de Estilo Comum

* **Tipografia**: família sans-serif (p.ex. Inter ou Poppins), com hierarquia clara: títulos semibold a bold, corpo regular.
* **Cores**:

  * Primária: Azul-marinho bem escuro (\~#1F2937)
  * Destaque: Dourado metálico (#D4AF37)
  * CTA: Azul-celeste vibrante (#3B82F6)
  * Neutras: Cinza-escuro (#374151) e bege/sepia para seções temáticas.
* **Formas**: bordas arredondadas (4–8 px) em botões e cartões, uso de hexágonos apenas na seção de métricas hero.
* **Sombreamentos**: sombras suaves para destacar camadas (cards sobre fundo).
* **Iconografia**: linhas finas, monocromáticas em dourado ou branco conforme o fundo.
* **Espaçamento**: generoso, com bastante “padding” interno nos cards e “margin” entre seções, garantindo leitura e foco nos CTAs.

No conjunto, o visual transmite sensação de sofisticação, urgência e exclusividade, unindo referências clássicas (estátuas) a uma paleta contemporânea de dark mode + dourado.



## 1. Regras de Negócio

1. **Cadastro e segurança**

   * Usuário ≥ 18 anos, 1 conta por CPF.
   * Confirmação de e-mail e validação de CPF para evitar múltiplas contas.
   * Autenticação por e-mail+senha (ou OAuth/OTP) e 2FA opcional.

2. **Moeda interna “Chronos”**

   * Só se joga com Chronos.
   * Formas de recarga: cartão (crédito/débito), Pix, PayPal, boleto.
   * Transferência P2P: usuário → usuário.

3. **Modelos de sorteio**

   * **Demo**: sala automática com bots. Gratuito.
   * **Individual**: de 2 até N jogadores (ex.: 50).
   * **X1 (Desafio)**: exatamente 2 jogadores.
   * **X1 em Grupo**: 2 grupos de M jogadores (ex.: 5 ou 10).
   * **(Fase 2) Batalha Mensal**: aberto de 1ª a último dia do mês; exércitos A vs B; entrada fixa R\$ 10,00.

4. **Valores de aposta**

   * Faixas fixas: R\$ 1,50; 2,50; 4,50; 8,50; 16,50; 32,50.
   * Cada sala usa um desses valores.

5. **Taxa da casa**

   * Fixo de R\$ 0,50 por jogador em **toda** sala.
   * Ex.: 10 jogadores × R\$ 1,50 = R\$ 15; casa fica com R\$ 5; prêmio = R\$ 10.

6. **Fluxo financeiro**

   * Ao **entrar** na sala, débito **imediato** de Chronos.
   * **Sem reembolso** se o usuário sair ou desconectar.
   * Prêmio creditado em Chronos na conta do vencedor.

7. **Transparência e auditoria**

   * PRNG criptográfico (crypto.getRandomValues / secrets).
   * Semente dinâmica: timestamp + hash SHA-256(ID\_sala+timestamp)+entropia de evento.
   * Logs assinados + hash imutável armazenados (ID\_sala, timestamp, participantes, seed ofuscada).
   * Código-fonte aberto ou relatórios disponibilizados para auditoria.

8. **Programa de afiliados**

   * Usuário pode inserir “código de convite”.
   * Ao 1º depósito ≥ X, o convidante recebe Y Chronos de bônus.

9. **Limites e controles anti-fraude**

   * Monitorar múltiplas contas por mesmo CPF/IP.
   * Bloqueio automático em caso de suspeita.
   * Failover manual autenticado se houver erro no sorteio.

---

## 2. Fluxo de Usuário (MVP)

```mermaid
flowchart TD
  A[Landing Page] --> B{Entrar / Cadastrar}
  B --> C[Cadastro] --> D[Confirmação e-mail]
  B --> E[Login]
  D --> F[Dashboard]
  E --> F[Dashboard]

  F --> G[Experimentar Demo] 
  F --> H[Recarregar Chronos]
  F --> I[Escolher Sorteio Real]

  H --> J[Processar Pagamento] --> F

  I --> K{Tipo de Sorteio}
  K --> L[Individual]
  K --> M[X1]
  K --> N[X1 em Grupo]

  L --> O[Selecionar valor de aposta]
  M --> P[Buscar Sala / Criar Sala (Pública ou Privada)]
  N --> Q[Buscar Grupo / Criar Grupo]

  O --> R[Confirmar participação]
  P --> R
  Q --> R

  R --> S[Aguardar sala preencher (2–5 min)]
  S --> T[Contagem regressiva 10 s + animação]
  T --> U[Sorteio → vencedor]
  U --> V[Crédito prêmio em Chronos]

  V --> F[Dashboard]  
```

**Detalhes extras**

* **Chat em sala**: troca de mensagens em tempo real (Socket.IO).
* **Perfis**: foto, nickname, dados bancários (para saque).
* **Histórico**: acesso a logs e ao relatório de cada sorteio.
* **Convites**: geração de link para partidas privadas.

---

## 3. Stack Mínima

| Camada               | Tech / API                                           |
| -------------------- | ---------------------------------------------------- |
| **Front-end Web**    | Next.js (React) + TailwindCSS                        |
| **Mobile / PWA**     | React Native ou PWA (Next.js)                        |
| **Autenticação**     | NextAuth (e-mail/senha) + JWT                        |
| **Back-end**         | Node.js + Express ou NestJS                          |
| **Real-time**        | Socket.IO                                            |
| **Banco de Dados**   | PostgreSQL (Prisma ORM)                              |
| **Cache / Pub-Sub**  | Redis (sessões + pub/sub)                            |
| **Pagamentos**       | Stripe (cartão), PayPal SDK, Pix via Gerencianet API |
| **Armazenamento**    | AWS S3 / DigitalOcean Spaces                         |
| **Deploy & Infra**   | Vercel (front) + AWS ECS ou Heroku (API)             |
| **CI/CD**            | GitHub Actions                                       |
| **Logs & Auditoria** | Elasticsearch + Kibana ou LogRocket                  |
| **CRYPTO**           | Node.js crypto (seed) + SHA-256 libs                 |

> **Observação:** para acelerar o MVP você pode, alternativamente, usar **Supabase** (auth + Postgres + Realtime) e **Stripe**, eliminando Redis e parte da infra inicial.

---

Com isso, você já tem as bases para começar o **MVP** em até 1 semana:

1. Configurar Projeto (Next.js + API)
2. Modelar esquema Prisma/Postgres
3. Integrar Stripe & gateways (Pix, PayPal)
4. Implementar salas com Socket.IO
5. Adicionar PRNG + logs criptografados
6. Testes end-to-end e deploy

Qualquer dúvida ou ajuste, só falar! 🚀
